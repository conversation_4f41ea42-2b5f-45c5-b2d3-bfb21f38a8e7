{"name": "mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"dev": "node start-enterprise.js", "start": "node start-enterprise.js", "android": "node start-enterprise.js --android", "ios": "node start-enterprise.js --ios", "web": "node start-enterprise.js --web", "start-enterprise": "node start-enterprise.js", "start-safe": "node fix-expo-cache.js", "start-fallback": "copy metro.config.fallback.js metro.config.js && node fix-expo-cache.js", "start-original": "npx expo start", "cache:validate": "node scripts/enterprise-cache-manager.js validate", "cache:clean": "node scripts/enterprise-cache-manager.js clean", "cache:optimize": "node scripts/enterprise-cache-manager.js optimize", "cache:reset": "node scripts/enterprise-cache-manager.js full-reset", "cache:legacy-clean": "node scripts/metro-cache-manager.js clean", "cache:legacy-validate": "node scripts/metro-cache-manager.js validate", "cache:legacy-fix-deps": "node scripts/metro-cache-manager.js fix-deps", "clear-cache": "npm run cache:clean && npm start", "reset-metro": "npm run cache:reset && npm start", "type-check": "tsc --noEmit"}, "dependencies": {"@babel/runtime": "^7.25.0", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "expo": "~52.0.0", "expo-asset": "^11.1.5", "expo-constants": "^17.1.6", "expo-dev-client": "~5.2.1", "expo-file-system": "^18.1.10", "expo-font": "^13.3.1", "expo-linear-gradient": "~14.1.5", "expo-status-bar": "~2.2.3", "fbjs": "^3.0.5", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.75.3", "react-native-safe-area-context": "^5.4.0", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-native-web-linear-gradient": "^1.1.2", "typescript": "~5.8.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/cli": "^0.24.15", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}
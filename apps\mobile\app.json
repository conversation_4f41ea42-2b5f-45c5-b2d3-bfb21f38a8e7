{"expo": {"name": "Tap2Go", "slug": "tap2go-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#f3a823"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.tap2go.mobile"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#f3a823"}, "package": "com.tap2go.mobile", "edgeToEdgeEnabled": true}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router"], "extra": {"eas": {"projectId": "72b189bb-ad13-4154-99aa-dd849cc3dbb2"}}, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}
{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": false, "skipLibCheck": true, "noImplicitAny": false, "jsx": "preserve", "jsxImportSource": "react", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "bundler", "noEmit": true, "target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "types": ["react", "react-native"], "paths": {"@/*": ["./src/*"], "react": ["../node_modules/@types/react"], "react-native": ["../node_modules/@types/react-native"]}, "incremental": true, "isolatedModules": true, "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", "nativewind-env.d.ts", "src/types/**/*.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", "scripts"]}